import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

interface VerificationRecord {
  phone: string;
  code: string;
  createdAt: string;
  expiresAt: string;
  attempts: number;
}

// 获取验证码数据文件路径
const getVerificationFilePath = () => {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  return path.join(dataDir, 'verifications.json');
};

// 读取验证码记录
const readVerifications = (): VerificationRecord[] => {
  const filePath = getVerificationFilePath();
  try {
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('读取验证码文件失败:', error);
  }
  return [];
};

// 保存验证码记录
const saveVerifications = (verifications: VerificationRecord[]) => {
  const filePath = getVerificationFilePath();
  try {
    fs.writeFileSync(filePath, JSON.stringify(verifications, null, 2), 'utf8');
  } catch (error) {
    console.error('保存验证码文件失败:', error);
    throw error;
  }
};

// 生成6位数字验证码
const generateVerificationCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 清理过期的验证码记录
const cleanExpiredVerifications = (verifications: VerificationRecord[]): VerificationRecord[] => {
  const now = new Date();
  return verifications.filter(v => new Date(v.expiresAt) > now);
};

// 发送验证码（模拟）
const sendSMS = async (phone: string, code: string): Promise<boolean> => {
  // 这里应该调用实际的短信服务API
  // 例如：阿里云短信、腾讯云短信等
  console.log(`模拟发送验证码到 ${phone}: ${code}`);
  
  // 模拟发送成功
  return true;
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { phone } = body;

    // 验证手机号格式
    if (!phone || typeof phone !== 'string' || !phone.trim()) {
      return NextResponse.json(
        { 
          success: false, 
          message: '手机号不能为空' 
        },
        { status: 400 }
      );
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return NextResponse.json(
        { 
          success: false, 
          message: '手机号格式不正确' 
        },
        { status: 400 }
      );
    }

    // 读取现有验证码记录
    let verifications = readVerifications();
    
    // 清理过期记录
    verifications = cleanExpiredVerifications(verifications);

    // 检查是否存在未过期的验证码
    const existingVerification = verifications.find(v => v.phone === phone);
    const now = new Date();

    if (existingVerification) {
      const timeSinceLastSent = now.getTime() - new Date(existingVerification.createdAt).getTime();
      const cooldownPeriod = 60 * 1000; // 60秒冷却期

      if (timeSinceLastSent < cooldownPeriod) {
        const remainingTime = Math.ceil((cooldownPeriod - timeSinceLastSent) / 1000);
        return NextResponse.json(
          { 
            success: false, 
            message: `请等待 ${remainingTime} 秒后再试` 
          },
          { status: 429 }
        );
      }
    }

    // 生成新的验证码
    const code = generateVerificationCode();
    const createdAt = now.toISOString();
    const expiresAt = new Date(now.getTime() + 5 * 60 * 1000).toISOString(); // 5分钟后过期

    // 发送短信
    try {
      const sendSuccess = await sendSMS(phone, code);
      if (!sendSuccess) {
        throw new Error('短信发送失败');
      }
    } catch (error) {
      console.error('发送短信失败:', error);
      return NextResponse.json(
        { 
          success: false, 
          message: '验证码发送失败，请稍后重试' 
        },
        { status: 500 }
      );
    }

    // 更新或添加验证码记录
    if (existingVerification) {
      existingVerification.code = code;
      existingVerification.createdAt = createdAt;
      existingVerification.expiresAt = expiresAt;
      existingVerification.attempts = 0;
    } else {
      verifications.push({
        phone,
        code,
        createdAt,
        expiresAt,
        attempts: 0
      });
    }

    // 保存记录
    saveVerifications(verifications);

    console.log(`验证码已发送到 ${phone}: ${code} (过期时间: ${expiresAt})`);

    return NextResponse.json({
      success: true,
      message: '验证码已发送',
      expiresIn: 300 // 5分钟
    });

  } catch (error) {
    console.error('发送验证码时出错:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: '服务器内部错误，请稍后重试' 
      },
      { status: 500 }
    );
  }
}

// 验证验证码
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { phone, code } = body;

    if (!phone || !code) {
      return NextResponse.json(
        { 
          success: false, 
          message: '手机号和验证码不能为空' 
        },
        { status: 400 }
      );
    }

    // 读取验证码记录
    let verifications = readVerifications();
    
    // 清理过期记录
    verifications = cleanExpiredVerifications(verifications);

    // 查找对应的验证码记录
    const verification = verifications.find(v => v.phone === phone);

    if (!verification) {
      return NextResponse.json(
        { 
          success: false, 
          message: '验证码不存在或已过期，请重新获取' 
        },
        { status: 400 }
      );
    }

    // 检查尝试次数
    if (verification.attempts >= 3) {
      return NextResponse.json(
        { 
          success: false, 
          message: '验证码尝试次数过多，请重新获取' 
        },
        { status: 400 }
      );
    }

    // 验证码错误
    if (verification.code !== code) {
      verification.attempts += 1;
      saveVerifications(verifications);
      
      return NextResponse.json(
        { 
          success: false, 
          message: `验证码错误，还可尝试 ${3 - verification.attempts} 次` 
        },
        { status: 400 }
      );
    }

    // 验证成功，删除验证码记录
    const updatedVerifications = verifications.filter(v => v.phone !== phone);
    saveVerifications(updatedVerifications);

    console.log(`验证码验证成功: ${phone}`);

    return NextResponse.json({
      success: true,
      message: '验证码验证成功'
    });

  } catch (error) {
    console.error('验证验证码时出错:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: '服务器内部错误，请稍后重试' 
      },
      { status: 500 }
    );
  }
}
