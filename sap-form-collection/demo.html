<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAP 表单收集系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .modal {
            display: none;
        }
        .modal.show {
            display: flex;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <div class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <!-- 顶部图片 -->
            <div class="bg-blue-600 p-6 text-center">
                <div class="w-32 h-16 mx-auto bg-white rounded flex items-center justify-center">
                    <span class="text-blue-600 font-bold text-2xl">SAP</span>
                </div>
            </div>

            <!-- 表单内容 -->
            <form id="sapForm" class="p-6 space-y-6">
                <!-- 姓名 -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        姓名 <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入您的姓名"
                    />
                    <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <!-- 手机号 -->
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                        手机号 <span class="text-red-500">*</span>
                    </label>
                    <div class="flex space-x-2">
                        <input
                            type="tel"
                            id="phone"
                            name="phone"
                            required
                            class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="请输入手机号"
                        />
                        <button
                            type="button"
                            id="sendCodeBtn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm whitespace-nowrap"
                        >
                            获取验证码
                        </button>
                    </div>
                    <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <!-- 验证码 -->
                <div>
                    <label for="verificationCode" class="block text-sm font-medium text-gray-700 mb-2">
                        验证码 <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="verificationCode"
                        name="verificationCode"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入验证码"
                    />
                    <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <!-- 公司全称 -->
                <div>
                    <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                        公司全称 <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="company"
                        name="company"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入公司全称"
                    />
                    <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <!-- 职务 -->
                <div>
                    <label for="position" class="block text-sm font-medium text-gray-700 mb-2">
                        您的职务 <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        id="position"
                        name="position"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入您的职务"
                    />
                    <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <!-- 邮箱 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        邮箱 <span class="text-red-500">*</span>
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入邮箱地址"
                    />
                    <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <!-- 隐私政策确认 -->
                <div>
                    <div class="flex items-start">
                        <input
                            type="checkbox"
                            id="privacyAgreed"
                            name="privacyAgreed"
                            required
                            class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label for="privacyAgreed" class="ml-2 text-sm text-gray-700 leading-relaxed">
                            您知悉并同意：上述您主动填写的表单涉及您的个人信息，其中您的特定身份信息属于个人敏感信息，收集前述信息是为了满足本项目调研需要。请您审慎评估后开始填写。您填写的信息将被存入36氪信息库，且为本项目调研之目的，您填写的信息将会被共享给SAP（思爱普）。除特别说明并获得授权之外，我们将按照36氪《隐私政策》处理您所提交的信息。
                        </label>
                    </div>
                    <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <!-- 提交按钮 -->
                <button
                    type="submit"
                    id="submitBtn"
                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
                >
                    提交
                </button>
            </form>
        </div>
    </div>

    <!-- 成功提交弹窗 -->
    <div id="successModal" class="modal fixed inset-0 bg-black bg-opacity-50 items-center justify-center z-50 p-4">
        <div class="bg-white rounded-lg p-6 max-w-sm w-full text-center">
            <div class="mb-4">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">感谢提交</h3>
            <p class="text-gray-600 mb-4">您的信息已成功提交，我们会尽快与您联系。</p>
            <button
                id="closeModalBtn"
                class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
                确定
            </button>
        </div>
    </div>

    <script>
        // 表单验证和提交逻辑
        let countdown = 0;
        let countdownTimer = null;

        // 获取验证码按钮点击事件
        document.getElementById('sendCodeBtn').addEventListener('click', function() {
            const phone = document.getElementById('phone').value;
            
            if (!phone) {
                showError('phone', '请输入手机号');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showError('phone', '请输入正确的手机号格式');
                return;
            }

            // 模拟发送验证码
            startCountdown();
            alert('验证码已发送到您的手机，请查收！（演示模式：验证码为 123456）');
            clearError('phone');
        });

        // 开始倒计时
        function startCountdown() {
            countdown = 60;
            const btn = document.getElementById('sendCodeBtn');
            btn.disabled = true;
            
            countdownTimer = setInterval(() => {
                countdown--;
                btn.textContent = `${countdown}s`;
                
                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                }
            }, 1000);
        }

        // 表单提交事件
        document.getElementById('sapForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 清除所有错误信息
            clearAllErrors();
            
            // 获取表单数据
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // 验证表单
            let hasError = false;
            
            if (!data.name.trim()) {
                showError('name', '请输入姓名');
                hasError = true;
            }
            
            if (!data.phone.trim()) {
                showError('phone', '请输入手机号');
                hasError = true;
            } else if (!/^1[3-9]\d{9}$/.test(data.phone)) {
                showError('phone', '请输入正确的手机号格式');
                hasError = true;
            }
            
            if (!data.verificationCode.trim()) {
                showError('verificationCode', '请输入验证码');
                hasError = true;
            } else if (data.verificationCode !== '123456') {
                showError('verificationCode', '验证码错误（演示模式：请输入 123456）');
                hasError = true;
            }
            
            if (!data.company.trim()) {
                showError('company', '请输入公司全称');
                hasError = true;
            }
            
            if (!data.position.trim()) {
                showError('position', '请输入您的职务');
                hasError = true;
            }
            
            if (!data.email.trim()) {
                showError('email', '请输入邮箱');
                hasError = true;
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
                showError('email', '请输入正确的邮箱格式');
                hasError = true;
            }
            
            if (!data.privacyAgreed) {
                showError('privacyAgreed', '请阅读并同意隐私政策');
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            // 模拟提交
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';
            
            setTimeout(() => {
                // 重置表单
                this.reset();
                submitBtn.disabled = false;
                submitBtn.textContent = '提交';
                
                // 显示成功弹窗
                document.getElementById('successModal').classList.add('show');
                
                // 保存数据到本地存储（演示用）
                const submissions = JSON.parse(localStorage.getItem('sapSubmissions') || '[]');
                submissions.push({
                    ...data,
                    id: Date.now().toString(),
                    submittedAt: new Date().toISOString()
                });
                localStorage.setItem('sapSubmissions', JSON.stringify(submissions));
                
                console.log('表单提交成功:', data);
            }, 1000);
        });

        // 关闭弹窗
        document.getElementById('closeModalBtn').addEventListener('click', function() {
            document.getElementById('successModal').classList.remove('show');
        });

        // 显示错误信息
        function showError(fieldName, message) {
            const field = document.getElementById(fieldName);
            const errorDiv = field.parentNode.querySelector('.error-message');
            field.classList.add('border-red-500');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
        }

        // 清除错误信息
        function clearError(fieldName) {
            const field = document.getElementById(fieldName);
            const errorDiv = field.parentNode.querySelector('.error-message');
            field.classList.remove('border-red-500');
            errorDiv.classList.add('hidden');
        }

        // 清除所有错误信息
        function clearAllErrors() {
            const errorMessages = document.querySelectorAll('.error-message');
            const fields = document.querySelectorAll('input');
            
            errorMessages.forEach(error => error.classList.add('hidden'));
            fields.forEach(field => field.classList.remove('border-red-500'));
        }

        // 输入时清除错误
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', function() {
                clearError(this.id || this.name);
            });
        });
    </script>
</body>
</html>
