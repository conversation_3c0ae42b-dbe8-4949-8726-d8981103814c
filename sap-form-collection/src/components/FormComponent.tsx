'use client';

import { useState } from 'react';
import Image from 'next/image';

interface FormData {
  name: string;
  phone: string;
  verificationCode: string;
  company: string;
  position: string;
  email: string;
  privacyAgreed: boolean;
}

interface FormErrors {
  name?: string;
  phone?: string;
  verificationCode?: string;
  company?: string;
  position?: string;
  email?: string;
  privacyAgreed?: string;
}

export default function FormComponent() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    phone: '',
    verificationCode: '',
    company: '',
    position: '',
    email: '',
    privacyAgreed: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 验证码倒计时
  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 发送验证码
  const sendVerificationCode = async () => {
    if (!formData.phone) {
      setErrors({ ...errors, phone: '请输入手机号' });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      setErrors({ ...errors, phone: '请输入正确的手机号格式' });
      return;
    }

    try {
      const response = await fetch('/api/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone: formData.phone }),
      });

      const result = await response.json();

      if (result.success) {
        setVerificationSent(true);
        startCountdown();
        alert('验证码已发送，请查收短信');
      } else {
        alert(result.message || '验证码发送失败');
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      alert('验证码发送失败，请稍后重试');
    }
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入姓名';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入正确的手机号格式';
    }

    if (!formData.verificationCode.trim()) {
      newErrors.verificationCode = '请输入验证码';
    }

    if (!formData.company.trim()) {
      newErrors.company = '请输入公司全称';
    }

    if (!formData.position.trim()) {
      newErrors.position = '请输入您的职务';
    }

    if (!formData.email.trim()) {
      newErrors.email = '请输入邮箱';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入正确的邮箱格式';
    }

    if (!formData.privacyAgreed) {
      newErrors.privacyAgreed = '请阅读并同意隐私政策';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 调用API提交表单
      const response = await fetch('/api/submit-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setShowSuccessModal(true);
        // 重置表单
        setFormData({
          name: '',
          phone: '',
          verificationCode: '',
          company: '',
          position: '',
          email: '',
          privacyAgreed: false,
        });
        setErrors({});
      } else {
        throw new Error('提交失败');
      }
    } catch (error) {
      console.error('提交错误:', error);
      alert('提交失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 输入框变化处理
  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData({ ...formData, [field]: value });
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors({ ...errors, [field]: undefined });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* 顶部图片 */}
        <div className="bg-blue-600 p-6 text-center">
          <div className="w-32 h-16 mx-auto bg-white rounded flex items-center justify-center">
            <Image
              src="/sap-logo.svg"
              alt="SAP Logo"
              width={120}
              height={60}
              className="object-contain"
            />
          </div>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 姓名 */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              姓名 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入您的姓名"
            />
            {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
          </div>

          {/* 手机号 */}
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
              手机号 <span className="text-red-500">*</span>
            </label>
            <div className="flex space-x-2">
              <input
                type="tel"
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={`flex-1 px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.phone ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="请输入手机号"
              />
              <button
                type="button"
                onClick={sendVerificationCode}
                disabled={countdown > 0}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm whitespace-nowrap"
              >
                {countdown > 0 ? `${countdown}s` : '获取验证码'}
              </button>
            </div>
            {errors.phone && <p className="mt-1 text-sm text-red-500">{errors.phone}</p>}
          </div>

          {/* 验证码 */}
          <div>
            <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-2">
              验证码 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="verificationCode"
              value={formData.verificationCode}
              onChange={(e) => handleInputChange('verificationCode', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.verificationCode ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入验证码"
            />
            {errors.verificationCode && <p className="mt-1 text-sm text-red-500">{errors.verificationCode}</p>}
          </div>

          {/* 公司全称 */}
          <div>
            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
              公司全称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="company"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.company ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入公司全称"
            />
            {errors.company && <p className="mt-1 text-sm text-red-500">{errors.company}</p>}
          </div>

          {/* 职务 */}
          <div>
            <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-2">
              您的职务 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="position"
              value={formData.position}
              onChange={(e) => handleInputChange('position', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.position ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入您的职务"
            />
            {errors.position && <p className="mt-1 text-sm text-red-500">{errors.position}</p>}
          </div>

          {/* 邮箱 */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              邮箱 <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入邮箱地址"
            />
            {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
          </div>

          {/* 隐私政策确认 */}
          <div>
            <div className="flex items-start">
              <input
                type="checkbox"
                id="privacyAgreed"
                checked={formData.privacyAgreed}
                onChange={(e) => handleInputChange('privacyAgreed', e.target.checked)}
                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="privacyAgreed" className="ml-2 text-sm text-gray-700 leading-relaxed">
                您知悉并同意：上述您主动填写的表单涉及您的个人信息，其中您的特定身份信息属于个人敏感信息，收集前述信息是为了满足本项目调研需要。请您审慎评估后开始填写。您填写的信息将被存入36氪信息库，且为本项目调研之目的，您填写的信息将会被共享给SAP（思爱普）。除特别说明并获得授权之外，我们将按照36氪《隐私政策》处理您所提交的信息。
              </label>
            </div>
            {errors.privacyAgreed && <p className="mt-1 text-sm text-red-500">{errors.privacyAgreed}</p>}
          </div>

          {/* 提交按钮 */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
          >
            {isSubmitting ? '提交中...' : '提交'}
          </button>
        </form>
      </div>

      {/* 成功提交弹窗 */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full text-center">
            <div className="mb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">感谢提交</h3>
            <p className="text-gray-600 mb-4">您的信息已成功提交，我们会尽快与您联系。</p>
            <button
              onClick={() => setShowSuccessModal(false)}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              确定
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
