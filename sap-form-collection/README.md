# SAP 表单收集系统

一个现代化的表单收集系统，支持移动端和PC端响应式设计，包含前端表单和后台管理功能。

## 功能特性

### 前端表单
- ✅ 响应式设计，支持移动端和PC端
- ✅ 顶部 SAP Logo 展示
- ✅ 完整的表单字段：
  - 姓名
  - 手机号（带验证码功能）
  - 公司全称
  - 职务
  - 邮箱
- ✅ 手机验证码发送和验证
- ✅ 隐私政策确认勾选
- ✅ 表单验证和错误提示
- ✅ 提交成功弹窗提醒

### 后台管理
- ✅ 数据列表展示
- ✅ 搜索功能
- ✅ 数据导出（CSV格式）
- ✅ 分页显示
- ✅ 数据管理（删除、清空）

## 快速开始

### 方式一：直接使用 HTML 演示版本（推荐）

1. 打开 `demo.html` 文件查看表单界面
2. 打开 `admin.html` 文件查看后台管理界面

**演示说明：**
- 验证码固定为：`123456`
- 数据存储在浏览器本地存储中
- 支持完整的表单验证和提交流程

### 方式二：使用 Next.js 完整版本

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 访问应用：
- 表单页面：http://localhost:3000
- 后台管理：http://localhost:3000/admin

## 项目结构

```
sap-form-collection/
├── demo.html              # 演示版表单页面
├── admin.html             # 演示版后台管理页面
├── src/
│   ├── app/
│   │   ├── page.tsx       # 主页面
│   │   ├── admin/
│   │   │   └── page.tsx   # 后台管理页面
│   │   └── api/
│   │       ├── submit-form/
│   │       │   └── route.ts    # 表单提交API
│   │       └── send-verification/
│   │           └── route.ts    # 验证码发送API
│   └── components/
│       └── FormComponent.tsx   # 表单组件
├── public/
│   └── sap-logo.svg       # SAP Logo
└── data/                  # 数据存储目录（自动创建）
    ├── submissions.json   # 表单提交数据
    └── verifications.json # 验证码记录
```

## API 接口

### 1. 发送验证码
- **POST** `/api/send-verification`
- 请求体：`{ "phone": "手机号" }`
- 功能：发送验证码到指定手机号

### 2. 验证验证码
- **PUT** `/api/send-verification`
- 请求体：`{ "phone": "手机号", "code": "验证码" }`
- 功能：验证验证码是否正确

### 3. 提交表单
- **POST** `/api/submit-form`
- 请求体：表单数据对象
- 功能：保存表单提交数据

### 4. 获取提交记录
- **GET** `/api/submit-form?page=1&limit=10&search=关键词`
- 功能：获取表单提交记录，支持分页和搜索

## 技术栈

- **前端框架：** Next.js 15 + React 19
- **样式框架：** Tailwind CSS
- **开发语言：** TypeScript
- **数据存储：** JSON 文件（可扩展为数据库）
- **响应式设计：** 移动端优先

## 隐私政策文案

系统包含完整的隐私政策确认文案：

> 您知悉并同意：上述您主动填写的表单涉及您的个人信息，其中您的特定身份信息属于个人敏感信息，收集前述信息是为了满足本项目调研需要。请您审慎评估后开始填写。您填写的信息将被存入36氪信息库，且为本项目调研之目的，您填写的信息将会被共享给SAP（思爱普）。除特别说明并获得授权之外，我们将按照36氪《隐私政策》处理您所提交的信息。

## 部署说明

### 生产环境部署

1. 构建项目：
```bash
npm run build
```

2. 启动生产服务器：
```bash
npm start
```

### 环境变量配置

可以通过环境变量配置：
- 短信服务API密钥
- 数据库连接信息
- 其他第三方服务配置

## 扩展功能

系统设计支持以下扩展：
- 集成真实的短信服务（阿里云、腾讯云等）
- 数据库存储（MySQL、PostgreSQL等）
- 用户认证和权限管理
- 数据分析和报表功能
- 邮件通知功能

## 许可证

MIT License
