<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAP 表单管理后台</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen bg-gray-50">
    <div class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <!-- 页面标题 -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">SAP 表单管理后台</h1>
                <p class="mt-2 text-gray-600">查看和管理表单提交数据</p>
            </div>

            <!-- 搜索和操作栏 -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <!-- 搜索表单 -->
                    <div class="flex gap-2">
                        <input
                            type="text"
                            id="searchInput"
                            placeholder="搜索姓名、手机号、公司或邮箱..."
                            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button
                            id="searchBtn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            搜索
                        </button>
                        <button
                            id="clearBtn"
                            class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            清除
                        </button>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-2">
                        <button
                            id="refreshBtn"
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                            刷新
                        </button>
                        <button
                            id="exportBtn"
                            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                            导出CSV
                        </button>
                        <button
                            id="clearDataBtn"
                            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                        >
                            清空数据
                        </button>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="mt-4 text-sm text-gray-600">
                    共 <span id="totalCount">0</span> 条记录
                    <span id="searchInfo" class="hidden"></span>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div id="loadingDiv" class="p-8 text-center hidden">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p class="mt-2 text-gray-600">加载中...</p>
                </div>

                <div id="emptyDiv" class="p-8 text-center text-gray-600 hidden">
                    暂无提交数据
                </div>

                <div id="tableContainer" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    姓名
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    手机号
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    公司
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    职务
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    邮箱
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    提交时间
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 数据行将通过 JavaScript 动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allSubmissions = [];
        let filteredSubmissions = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSubmissions();
            bindEvents();
        });

        // 绑定事件
        function bindEvents() {
            document.getElementById('searchBtn').addEventListener('click', handleSearch);
            document.getElementById('clearBtn').addEventListener('click', handleClear);
            document.getElementById('refreshBtn').addEventListener('click', loadSubmissions);
            document.getElementById('exportBtn').addEventListener('click', exportToCSV);
            document.getElementById('clearDataBtn').addEventListener('click', clearAllData);
            
            // 搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });
        }

        // 加载提交数据
        function loadSubmissions() {
            showLoading(true);
            
            // 从本地存储获取数据
            const submissions = JSON.parse(localStorage.getItem('sapSubmissions') || '[]');
            allSubmissions = submissions.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
            filteredSubmissions = [...allSubmissions];
            
            updateDisplay();
            showLoading(false);
        }

        // 搜索处理
        function handleSearch() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            
            if (!searchTerm) {
                filteredSubmissions = [...allSubmissions];
                document.getElementById('searchInfo').classList.add('hidden');
            } else {
                filteredSubmissions = allSubmissions.filter(submission => 
                    submission.name.toLowerCase().includes(searchTerm) ||
                    submission.phone.includes(searchTerm) ||
                    submission.company.toLowerCase().includes(searchTerm) ||
                    submission.email.toLowerCase().includes(searchTerm)
                );
                
                const searchInfo = document.getElementById('searchInfo');
                searchInfo.textContent = ` (搜索: "${document.getElementById('searchInput').value}")`;
                searchInfo.classList.remove('hidden');
            }
            
            updateDisplay();
        }

        // 清除搜索
        function handleClear() {
            document.getElementById('searchInput').value = '';
            filteredSubmissions = [...allSubmissions];
            document.getElementById('searchInfo').classList.add('hidden');
            updateDisplay();
        }

        // 更新显示
        function updateDisplay() {
            document.getElementById('totalCount').textContent = filteredSubmissions.length;
            
            const tableContainer = document.getElementById('tableContainer');
            const emptyDiv = document.getElementById('emptyDiv');
            
            if (filteredSubmissions.length === 0) {
                tableContainer.classList.add('hidden');
                emptyDiv.classList.remove('hidden');
            } else {
                tableContainer.classList.remove('hidden');
                emptyDiv.classList.add('hidden');
                renderTable();
            }
        }

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';
            
            filteredSubmissions.forEach((submission, index) => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${escapeHtml(submission.name)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${escapeHtml(submission.phone)}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="${escapeHtml(submission.company)}">
                        ${escapeHtml(submission.company)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${escapeHtml(submission.position)}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title="${escapeHtml(submission.email)}">
                        ${escapeHtml(submission.email)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${formatDate(submission.submittedAt)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button 
                            onclick="deleteSubmission('${submission.id}')"
                            class="text-red-600 hover:text-red-900 font-medium"
                        >
                            删除
                        </button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 删除单条记录
        function deleteSubmission(id) {
            if (!confirm('确定要删除这条记录吗？')) {
                return;
            }
            
            allSubmissions = allSubmissions.filter(s => s.id !== id);
            localStorage.setItem('sapSubmissions', JSON.stringify(allSubmissions));
            
            // 重新过滤和显示
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            if (searchTerm) {
                handleSearch();
            } else {
                filteredSubmissions = [...allSubmissions];
                updateDisplay();
            }
        }

        // 导出CSV
        function exportToCSV() {
            if (filteredSubmissions.length === 0) {
                alert('没有数据可导出');
                return;
            }

            const headers = ['ID', '姓名', '手机号', '公司全称', '职务', '邮箱', '提交时间'];
            const csvContent = [
                headers.join(','),
                ...filteredSubmissions.map(submission => [
                    submission.id,
                    `"${submission.name}"`,
                    submission.phone,
                    `"${submission.company}"`,
                    `"${submission.position}"`,
                    submission.email,
                    formatDate(submission.submittedAt)
                ].join(','))
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `sap-form-submissions-${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 清空所有数据
        function clearAllData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                return;
            }
            
            localStorage.removeItem('sapSubmissions');
            allSubmissions = [];
            filteredSubmissions = [];
            updateDisplay();
            alert('数据已清空');
        }

        // 显示加载状态
        function showLoading(show) {
            const loadingDiv = document.getElementById('loadingDiv');
            const tableContainer = document.getElementById('tableContainer');
            
            if (show) {
                loadingDiv.classList.remove('hidden');
                tableContainer.classList.add('hidden');
            } else {
                loadingDiv.classList.add('hidden');
            }
        }

        // 格式化日期
        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
